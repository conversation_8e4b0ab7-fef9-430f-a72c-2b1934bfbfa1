[package]
name = "dw-test-item"
version = "0.1.0"
edition = "2021"

[dependencies]
parquet-provider = { path = '../parquet-provider' }
mysql-provider = { path = '../mysql-provider' }
ck-provider = { path = '../ck-provider' }
common = { path = '../common' }
log = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
java-properties = "1.4.1"
anyhow = "1.0"
thiserror = { workspace = true }
rand = "0.8"
tokio = { workspace = true }
rust_decimal = { workspace = true }
chrono = { workspace = true }
clickhouse = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["time", "local-time"] }
color-eyre = { workspace = true }
bumpalo = { workspace = true }
rayon = { workspace = true }
sqlx = { workspace = true }
dashmap = "6.1"
regex = "1.10"
